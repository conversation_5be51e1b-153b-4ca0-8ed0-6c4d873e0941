"""
数据库CRUD操作
包含用户、消息记录、试用记录的增删改查操作
"""
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from datetime import date, datetime
from typing import Optional, List
from database.models import User, MessageRecord, TrialRecord

# 用户相关操作
def get_user_by_username(db: Session, username: str) -> Optional[User]:
    """根据用户名获取用户"""
    return db.query(User).filter(User.username == username).first()

def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
    """根据ID获取用户"""
    return db.query(User).filter(User.id == user_id).first()

def create_user(db: Session, username: str, password_hash: str, nickname: str) -> User:
    """创建新用户"""
    db_user = User(
        username=username,
        password_hash=password_hash,
        nickname=nickname
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

# 消息记录相关操作
def create_message_record(db: Session, user_id: Optional[int], trial_cookie: Optional[str], message_content: str) -> MessageRecord:
    """创建消息记录"""
    db_record = MessageRecord(
        user_id=user_id,
        trial_cookie=trial_cookie,
        message_content=message_content,
        date=date.today()
    )
    db.add(db_record)
    db.commit()
    db.refresh(db_record)
    return db_record

def get_user_daily_message_count(db: Session, user_id: int, target_date: date = None) -> int:
    """获取用户指定日期的消息发送数量"""
    if target_date is None:
        target_date = date.today()
    
    count = db.query(MessageRecord).filter(
        and_(
            MessageRecord.user_id == user_id,
            MessageRecord.date == target_date
        )
    ).count()
    return count

# 试用记录相关操作
def get_trial_record_by_cookie(db: Session, cookie_value: str) -> Optional[TrialRecord]:
    """根据cookie获取试用记录"""
    return db.query(TrialRecord).filter(TrialRecord.cookie_value == cookie_value).first()

def create_trial_record(db: Session, cookie_value: str) -> TrialRecord:
    """创建试用记录"""
    db_record = TrialRecord(cookie_value=cookie_value)
    db.add(db_record)
    db.commit()
    db.refresh(db_record)
    return db_record

def update_trial_usage(db: Session, cookie_value: str) -> Optional[TrialRecord]:
    """更新试用使用次数"""
    trial_record = get_trial_record_by_cookie(db, cookie_value)
    if trial_record:
        trial_record.usage_count += 1
        trial_record.last_used_at = datetime.utcnow()
        db.commit()
        db.refresh(trial_record)
    return trial_record

def get_trial_usage_count(db: Session, cookie_value: str) -> int:
    """获取试用使用次数"""
    trial_record = get_trial_record_by_cookie(db, cookie_value)
    return trial_record.usage_count if trial_record else 0
