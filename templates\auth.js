/**
 * 认证管理模块
 * 处理用户登录、注册、状态管理等功能
 */

// 认证状态管理
class AuthManager {
    constructor() {
        this.token = localStorage.getItem('auth_token');
        this.user = this.token ? JSON.parse(localStorage.getItem('user_info') || '{}') : null;
        this.trialCookie = this.getCookie('trial_cookie');
    }

    // 获取Cookie
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }

    // 设置认证信息
    setAuth(token, user) {
        this.token = token;
        this.user = user;
        localStorage.setItem('auth_token', token);
        localStorage.setItem('user_info', JSON.stringify(user));
    }

    // 清除认证信息
    clearAuth() {
        this.token = null;
        this.user = null;
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_info');
    }

    // 检查是否已登录
    isLoggedIn() {
        return !!this.token && !!this.user;
    }

    // 获取认证头
    getAuthHeaders() {
        return this.token ? { 'Authorization': `Bearer ${this.token}` } : {};
    }

    // 登录
    async login(username, password) {
        const response = await fetch('/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();
        
        if (response.ok) {
            this.setAuth(data.access_token, data.user);
            return { success: true, user: data.user };
        } else {
            return { success: false, error: data.detail || '登录失败' };
        }
    }

    // 注册
    async register(username, password, nickname) {
        const response = await fetch('/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password, nickname })
        });

        const data = await response.json();
        
        if (response.ok) {
            return { success: true, message: data.message };
        } else {
            return { success: false, error: data.detail || '注册失败' };
        }
    }

    // 登出
    async logout() {
        try {
            await fetch('/auth/logout', {
                method: 'POST',
                headers: this.getAuthHeaders()
            });
        } catch (error) {
            console.error('登出请求失败:', error);
        }
        
        this.clearAuth();
        window.location.href = '/';
    }

    // 获取用户信息
    async getUserInfo() {
        if (!this.token) return null;

        try {
            const response = await fetch('/auth/me', {
                headers: this.getAuthHeaders()
            });

            if (response.ok) {
                const user = await response.json();
                this.user = user;
                localStorage.setItem('user_info', JSON.stringify(user));
                return user;
            } else {
                // Token可能已过期
                this.clearAuth();
                return null;
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
            return null;
        }
    }

    // 初始化试用
    async initTrial() {
        try {
            const response = await fetch('/trial/init', {
                method: 'POST',
                credentials: 'include'
            });

            if (response.ok) {
                const data = await response.json();
                this.trialCookie = data.trial_cookie;
                return data;
            }
        } catch (error) {
            console.error('初始化试用失败:', error);
        }
        return null;
    }

    // 检查试用状态
    async checkTrialStatus() {
        try {
            const response = await fetch('/trial/check', {
                credentials: 'include'
            });

            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            console.error('检查试用状态失败:', error);
        }
        return null;
    }
}

// 全局认证管理器实例
const authManager = new AuthManager();

// 表单验证函数
function validateUsername(username) {
    if (username.length < 3 || username.length > 20) {
        return '用户名长度必须在3-20位之间';
    }
    if (!/^[a-zA-Z0-9_]+$/.test(username)) {
        return '用户名只能包含字母、数字和下划线';
    }
    return null;
}

function validatePassword(password) {
    if (password.length < 8) {
        return '密码长度至少8位';
    }
    if (!/[a-zA-Z]/.test(password)) {
        return '密码必须包含字母';
    }
    if (!/\d/.test(password)) {
        return '密码必须包含数字';
    }
    return null;
}

function validateNickname(nickname) {
    if (nickname.length < 1 || nickname.length > 50) {
        return '昵称长度必须在1-50位之间';
    }
    return null;
}

// 显示错误信息
function showError(elementId, message) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = message;
        element.style.display = message ? 'block' : 'none';
    }
}

// 清除所有错误信息
function clearErrors() {
    const errorElements = document.querySelectorAll('.error-message');
    errorElements.forEach(element => {
        element.textContent = '';
        element.style.display = 'none';
    });
}

// 设置按钮加载状态
function setButtonLoading(buttonId, loading) {
    const button = document.getElementById(buttonId);
    if (button) {
        if (loading) {
            button.classList.add('loading');
            button.disabled = true;
        } else {
            button.classList.remove('loading');
            button.disabled = false;
        }
    }
}

// 初始化登录页面
function initLoginPage() {
    const loginForm = document.getElementById('loginForm');
    if (!loginForm) return;

    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        clearErrors();
        setButtonLoading('loginBtn', true);

        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;

        // 基本验证
        if (!username) {
            showError('usernameError', '请输入用户名');
            setButtonLoading('loginBtn', false);
            return;
        }

        if (!password) {
            showError('passwordError', '请输入密码');
            setButtonLoading('loginBtn', false);
            return;
        }

        try {
            const result = await authManager.login(username, password);

            if (result.success) {
                showError('successMessage', '登录成功，正在跳转...');
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            } else {
                showError('generalError', result.error);
            }
        } catch (error) {
            showError('generalError', '登录失败，请稍后重试');
            console.error('登录错误:', error);
        }

        setButtonLoading('loginBtn', false);
    });
}

// 初始化注册页面
function initRegisterPage() {
    const registerForm = document.getElementById('registerForm');
    if (!registerForm) return;

    // 实时验证
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    const nicknameInput = document.getElementById('nickname');

    usernameInput.addEventListener('blur', function() {
        const error = validateUsername(this.value.trim());
        showError('usernameError', error);
    });

    passwordInput.addEventListener('blur', function() {
        const error = validatePassword(this.value);
        showError('passwordError', error);
    });

    confirmPasswordInput.addEventListener('blur', function() {
        const password = passwordInput.value;
        const confirmPassword = this.value;
        if (password && confirmPassword && password !== confirmPassword) {
            showError('confirmPasswordError', '两次输入的密码不一致');
        } else {
            showError('confirmPasswordError', '');
        }
    });

    nicknameInput.addEventListener('blur', function() {
        const error = validateNickname(this.value.trim());
        showError('nicknameError', error);
    });

    registerForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        clearErrors();
        setButtonLoading('registerBtn', true);

        const username = usernameInput.value.trim();
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        const nickname = nicknameInput.value.trim();

        // 验证所有字段
        let hasError = false;

        const usernameError = validateUsername(username);
        if (usernameError) {
            showError('usernameError', usernameError);
            hasError = true;
        }

        const passwordError = validatePassword(password);
        if (passwordError) {
            showError('passwordError', passwordError);
            hasError = true;
        }

        if (password !== confirmPassword) {
            showError('confirmPasswordError', '两次输入的密码不一致');
            hasError = true;
        }

        const nicknameError = validateNickname(nickname);
        if (nicknameError) {
            showError('nicknameError', nicknameError);
            hasError = true;
        }

        if (hasError) {
            setButtonLoading('registerBtn', false);
            return;
        }

        try {
            const result = await authManager.register(username, password, nickname);

            if (result.success) {
                showError('successMessage', '注册成功！正在跳转到登录页面...');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 1500);
            } else {
                showError('generalError', result.error);
            }
        } catch (error) {
            showError('generalError', '注册失败，请稍后重试');
            console.error('注册错误:', error);
        }

        setButtonLoading('registerBtn', false);
    });
}
