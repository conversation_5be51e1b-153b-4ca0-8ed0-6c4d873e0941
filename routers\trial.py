"""
试用限制路由
处理未登录用户的试用功能
"""
from fastapi import APIRouter, Depends, HTTPException, status, Response, Cookie
from sqlalchemy.orm import Session
from database.database import get_db
from database.crud import (
    get_trial_record_by_cookie, create_trial_record, 
    update_trial_usage, get_trial_usage_count
)
from models.schemas import TrialCheckResponse
from auth.auth import generate_trial_cookie, TRIAL_LIMIT
from typing import Optional

router = APIRouter(prefix="/trial", tags=["试用限制"])

@router.post("/init", response_model=TrialCheckResponse)
async def init_trial(
    response: Response,
    trial_cookie: Optional[str] = Cookie(None, alias="trial_cookie"),
    db: Session = Depends(get_db)
):
    """初始化试用cookie"""
    try:
        if trial_cookie:
            # 检查现有cookie
            trial_record = get_trial_record_by_cookie(db, trial_cookie)
            if trial_record:
                return TrialCheckResponse(
                    success=True,
                    trial_cookie=trial_cookie,
                    usage_count=trial_record.usage_count,
                    remaining_count=TRIAL_LIMIT - trial_record.usage_count,
                    limit_reached=trial_record.usage_count >= TRIAL_LIMIT
                )
        
        # 生成新的试用cookie
        new_cookie = generate_trial_cookie()
        trial_record = create_trial_record(db, new_cookie)
        
        # 设置cookie（30天过期）
        response.set_cookie(
            key="trial_cookie",
            value=new_cookie,
            max_age=30 * 24 * 60 * 60,  # 30天
            httponly=True,
            secure=False,  # 开发环境设为False，生产环境应设为True
            samesite="lax"
        )
        
        return TrialCheckResponse(
            success=True,
            trial_cookie=new_cookie,
            usage_count=0,
            remaining_count=TRIAL_LIMIT,
            limit_reached=False
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"初始化试用失败: {str(e)}"
        )

@router.get("/check", response_model=TrialCheckResponse)
async def check_trial_status(
    trial_cookie: Optional[str] = Cookie(None, alias="trial_cookie"),
    db: Session = Depends(get_db)
):
    """检查试用状态"""
    try:
        if not trial_cookie:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少试用标识"
            )
        
        usage_count = get_trial_usage_count(db, trial_cookie)
        
        return TrialCheckResponse(
            success=True,
            trial_cookie=trial_cookie,
            usage_count=usage_count,
            remaining_count=TRIAL_LIMIT - usage_count,
            limit_reached=usage_count >= TRIAL_LIMIT
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检查试用状态失败: {str(e)}"
        )

@router.post("/use")
async def use_trial(
    trial_cookie: Optional[str] = Cookie(None, alias="trial_cookie"),
    db: Session = Depends(get_db)
):
    """使用一次试用机会"""
    try:
        if not trial_cookie:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少试用标识"
            )
        
        # 检查试用次数
        usage_count = get_trial_usage_count(db, trial_cookie)
        if usage_count >= TRIAL_LIMIT:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"试用次数已用完（{TRIAL_LIMIT}次），请注册账号继续使用"
            )
        
        # 更新使用次数
        trial_record = update_trial_usage(db, trial_cookie)
        if not trial_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="试用记录不存在"
            )
        
        return {
            "success": True,
            "message": "试用次数已更新",
            "usage_count": trial_record.usage_count,
            "remaining_count": TRIAL_LIMIT - trial_record.usage_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新试用次数失败: {str(e)}"
        )
