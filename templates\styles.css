/* Excel对话编辑系统样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 2.5em;
}

.section {
    background: white;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.section-header {
    background: #3498db;
    color: white;
    padding: 15px 20px;
    font-weight: bold;
    font-size: 1.1em;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-button {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.header-button:hover {
    background: rgba(255, 255, 255, 0.3);
}

.section-content {
    padding: 20px;
    padding-bottom: 200px;
}

.fixed-bottom-chat-input {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 800px;
    background-color: white;
    padding: 0;
    border: 1px solid #e5e5e5;
    border-radius: 20px;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    box-sizing: border-box;
}

.chat-input-container {
    position: relative;
    display: flex;
    align-items: flex-end;
    padding: 15px 20px;
    gap: 10px;
}

.chat-input-wrapper {
    flex: 1;
    position: relative;
}

.chat-textarea {
    width: 100%;
    padding: 12px 50px 12px 15px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    line-height: 1.4;
    resize: none;
    outline: none;
    background: transparent;
    min-height: 24px;
    max-height: 120px;
    overflow-y: auto;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
}

.chat-textarea::placeholder {
    color: #999;
}

.send-button {
    position: absolute;
    right: 8px;
    bottom: 8px;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 8px;
    background: #007bff;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    font-size: 14px;
}

.send-button:hover {
    background: #0056b3;
    transform: scale(1.05);
}

.send-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.send-arrow {
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 6px solid currentColor;
    transform: rotate(180deg);
}

.loading-spinner {
    width: 12px;
    height: 12px;
    border: 2px solid #fff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.message {
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}

.message.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.message.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.message.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

@media (max-width: 768px) {
    .grid {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 10px;
    }
}

.file-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0;
    margin: 10px 0;
    font-size: 13px;
    color: #6c757d;
    box-shadow: none;
    overflow: hidden;
}

.file-info-header {
    background: #e9ecef;
    color: #495057;
    padding: 12px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    font-size: 14px;
    transition: background-color 0.3s;
    user-select: none;
}

.file-info-header:hover {
    background: #dee2e6;
}

.file-info-toggle {
    font-size: 12px;
    transition: transform 0.3s;
}

.file-info-toggle.expanded {
    transform: rotate(180deg);
}

.file-info-content {
    padding: 12px;
    display: none;
}

.file-info-content.show {
    display: block;
}

.file-info h4 {
    color: #6c757d;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
}

/* 文件信息折叠样式 */
.file-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 10px;
    transition: background-color 0.3s;
}

.file-info-header:hover {
    background-color: rgba(0,0,0,0.02);
    border-radius: 4px;
    padding: 8px;
    margin: -8px 0 10px -8px;
}

.file-info-header h4 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.file-info-toggle {
    font-size: 12px;
    color: #6c757d;
    transition: transform 0.3s;
}

.file-info-toggle.collapsed {
    transform: rotate(-90deg);
}

.file-info-content {
    display: none;
    transition: all 0.3s ease;
}

.file-info-content.show {
    display: block;
}

.sheet-info {
    margin-left: 12px;
    margin-bottom: 6px;
    font-size: 12px;
    color: #868e96;
}

.chat-box {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    height: 600px;
    overflow-y: auto;
    background: #f9f9f9;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chat-message {
    margin-bottom: 20px;
    padding: 16px;
    border-radius: 12px;
    line-height: 1.6;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.chat-message.user {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-left: 4px solid #2196f3;
    margin-left: 40px;
}

.chat-message.assistant {
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
    border-left: 4px solid #4caf50;
    margin-right: 40px;
}

.code-block {
    background: #f4f4f4;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    overflow-x: auto;
    margin: 10px 0;
}

.hidden {
    display: none;
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Tab 样式 */
.tab-container {
    margin-bottom: 20px;
}

.tab-buttons {
    display: flex;
    background: white;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.tab-button {
    flex: 1;
    padding: 15px 20px;
    background: #ecf0f1;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    color: #7f8c8d;
    transition: all 0.3s;
    border-bottom: 3px solid transparent;
}

.tab-button.active {
    background: #3498db;
    color: white;
    border-bottom: 3px solid #2980b9;
}

.tab-button:not(.active) {
    background: #ecf0f1;
    color: #7f8c8d;
    border-bottom: 3px solid transparent;
}

.tab-button:hover:not(.active) {
    background: #d5dbdb;
    color: #2c3e50;
}

.tab-content {
    display: none;
    background: white;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 20px;
}

.tab-content.active {
    display: block;
}

.download-link {
    background: #27ae60;
    color: white !important;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    display: inline-block;
    margin: 5px 0;
    transition: background-color 0.3s;
}

.download-link:hover {
    background: #229954;
    text-decoration: none;
}

/* 对话历史相关样式 */
.conversation-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: box-shadow 0.3s;
}

.conversation-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.conversation-title {
    font-weight: bold;
    color: #495057;
    font-size: 16px;
}

.conversation-meta {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 10px;
}

.conversation-actions {
    display: flex;
    gap: 10px;
}

.btn-small {
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.empty-conversations {
    text-align: center;
    color: #6c757d;
    padding: 40px;
    font-style: italic;
}

.current-conversation-badge {
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

/* 嵌入式思考过程样式 */
.thinking-process {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin: 10px 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.thinking-header {
    color: #666;
    font-size: 14px;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 6px 6px 0 0;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s;
}

.thinking-header:hover {
    background: #e9ecef;
}

.thinking-header.active {
    background: #e3f2fd;
}

.thinking-time {
    font-size: 12px;
    opacity: 0.9;
}

.thinking-toggle {
    font-size: 12px;
    transition: transform 0.3s;
}

.thinking-toggle.expanded {
    transform: rotate(180deg);
}

.thinking-content {
    display: none;
    padding: 12px;
    background: white;
    max-height: 300px;
    overflow-y: auto;
}

.thinking-content.show {
    display: block;
}

.thinking-spinner-text {
    color: #666;
    font-style: italic;
}

.thinking-step {
    margin-bottom: 8px;
    padding: 6px 10px;
    border-radius: 4px;
    display: flex;
    align-items: flex-start;
    font-size: 13px;
    line-height: 1.4;
}

.thinking-step.pending {
    background: #fff3cd;
    border-left: 3px solid #ffc107;
    color: #856404;
}

.thinking-step.processing {
    background: #d1ecf1;
    border-left: 3px solid #17a2b8;
    color: #0c5460;
}

.thinking-step.success {
    background: #d4edda;
    border-left: 3px solid #28a745;
    color: #155724;
}

.thinking-step.error {
    background: #f8d7da;
    border-left: 3px solid #dc3545;
    color: #721c24;
}

.thinking-step-icon {
    margin-right: 8px;
    font-weight: bold;
    flex-shrink: 0;
}

.thinking-step-content {
    flex: 1;
}

.thinking-spinner {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #17a2b8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.thinking-code-preview {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px;
    margin-top: 8px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    max-height: 120px;
    overflow-y: auto;
    white-space: pre-wrap;
}

.thinking-output {
    background: #272822;
    color: #f8f8f2;
    border-radius: 4px;
    padding: 8px;
    margin-top: 8px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    max-height: 100px;
    overflow-y: auto;
    white-space: pre-wrap;
}

/* 修改聊天消息样式以更好适配思考过程 */
.chat-message.assistant {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin: 10px 0;
}

.chat-message.user {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 12px;
    margin: 10px 0;
}

/* Markdown样式 */
.markdown-content {
    line-height: 1.6;
}

.markdown-content h1, .markdown-content h2, .markdown-content h3 {
    margin-top: 20px;
    margin-bottom: 10px;
}

.markdown-content h1 {
    font-size: 1.8em;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 5px;
}

.markdown-content h2 {
    font-size: 1.5em;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 3px;
}

.markdown-content h3 {
    font-size: 1.3em;
}

.markdown-content p {
    margin-bottom: 10px;
}

.markdown-content ul, .markdown-content ol {
    margin-bottom: 10px;
    padding-left: 25px;
}

.markdown-content li {
    margin-bottom: 5px;
}

.markdown-content blockquote {
    border-left: 4px solid #3498db;
    padding-left: 15px;
    margin: 10px 0;
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 0 4px 4px 0;
}

.markdown-content code {
    background: #f1f3f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
    font-size: 0.9em;
}

.markdown-content pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    overflow-x: auto;
    margin: 10px 0;
}

.markdown-content pre code {
    background: none;
    padding: 0;
    border-radius: 0;
    font-size: 0.9em;
}

.markdown-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

.markdown-content th, .markdown-content td {
    border: 1px solid #e9ecef;
    padding: 8px 12px;
    text-align: left;
}

.markdown-content th {
    background: #f8f9fa;
    font-weight: bold;
}

.markdown-content a {
    color: #3498db;
    text-decoration: none;
}

.markdown-content a:hover {
    text-decoration: underline;
}

.markdown-content hr {
    border: none;
    border-top: 1px solid #e9ecef;
    margin: 20px 0;
}

.markdown-content strong {
    font-weight: bold;
}

.markdown-content em {
    font-style: italic;
}

/* 其他页面元素的通用样式 */
.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

input[type="text"], input[type="password"], textarea:not(.chat-textarea), select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
}

input[type="text"]:focus, input[type="password"]:focus, textarea:not(.chat-textarea):focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

input[type="file"] {
    width: 100%;
    padding: 8px;
    border: 2px dashed #ddd;
    border-radius: 4px;
    background: #fafafa;
}

button:not(.send-button):not(.tab-button) {
    background: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

button:not(.send-button):not(.tab-button):hover {
    background: #2980b9;
}

button:not(.send-button):not(.tab-button):disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

.btn-success {
    background: #27ae60;
}

.btn-success:hover {
    background: #229954;
}

.btn-danger {
    background: #e74c3c;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-upload {
    background: #2ecc71;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s;
    display: inline-block;
    text-align: center;
    min-width: 200px;
}

.btn-upload:hover {
    background: #27ae60;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}
