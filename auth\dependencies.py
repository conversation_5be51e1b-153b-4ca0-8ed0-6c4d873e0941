"""
认证依赖注入
提供FastAPI依赖注入函数
"""
from fastapi import Depends, HTTPException, status, Request, Cookie
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional
from database.database import get_db
from database.crud import get_user_by_id, get_trial_usage_count, get_user_daily_message_count
from database.models import User
from auth.auth import verify_token, TRIAL_LIMIT, DAILY_MESSAGE_LIMIT
from datetime import date

# HTTP Bearer认证
security = HTTPBearer(auto_error=False)

async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """获取当前登录用户（可选）"""
    if not credentials:
        return None
    
    payload = verify_token(credentials.credentials)
    if not payload:
        return None
    
    user_id = payload.get("sub")
    if not user_id:
        return None
    
    try:
        user_id = int(user_id)
        user = get_user_by_id(db, user_id)
        return user if user and user.is_active else None
    except (ValueError, TypeError):
        return None

async def get_current_user_required(
    current_user: Optional[User] = Depends(get_current_user)
) -> User:
    """获取当前登录用户（必需）"""
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要登录",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return current_user

async def check_message_quota(
    current_user: Optional[User] = Depends(get_current_user),
    trial_cookie: Optional[str] = Cookie(None, alias="trial_cookie"),
    db: Session = Depends(get_db)
) -> dict:
    """检查消息发送配额"""
    if current_user:
        # 登录用户：检查每日消息限制
        today_count = get_user_daily_message_count(db, current_user.id)
        if today_count >= DAILY_MESSAGE_LIMIT:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"今日消息发送已达上限（{DAILY_MESSAGE_LIMIT}条），请明天再试"
            )
        return {
            "user_id": current_user.id,
            "trial_cookie": None,
            "remaining_quota": DAILY_MESSAGE_LIMIT - today_count,
            "quota_type": "daily"
        }
    else:
        # 未登录用户：检查试用限制
        if not trial_cookie:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少试用标识"
            )
        
        usage_count = get_trial_usage_count(db, trial_cookie)
        if usage_count >= TRIAL_LIMIT:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"试用次数已用完（{TRIAL_LIMIT}次），请注册账号继续使用"
            )
        
        return {
            "user_id": None,
            "trial_cookie": trial_cookie,
            "remaining_quota": TRIAL_LIMIT - usage_count,
            "quota_type": "trial"
        }
