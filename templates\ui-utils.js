/**
 * UI工具函数和辅助功能
 * 包含消息显示、切换功能、思考过程显示等
 */

// Tab切换功能
function switchTab(tabName) {
    // 隐藏所有tab内容
    const allTabs = document.querySelectorAll('.tab-content');
    allTabs.forEach(tab => tab.classList.remove('active'));
    
    // 移除所有按钮的active类
    const allButtons = document.querySelectorAll('.tab-button');
    allButtons.forEach(btn => btn.classList.remove('active'));
    
    // 显示选中的tab
    document.getElementById(tabName + '-tab').classList.add('active');
    
    // 激活对应的按钮 - 通过onclick属性来找到对应按钮
    const targetButton = document.querySelector(`button[onclick="switchTab('${tabName}')"]`);
    if (targetButton) {
        targetButton.classList.add('active');
    }
    
    // 如果切换到对话历史tab，自动加载对话列表
    if (tabName === 'conversations') {
        loadConversations();
    }
}

// 定义 toggleFileInfo 函数
function toggleFileInfo(elementId) {
    const fileInfoElement = document.getElementById(elementId);
    if (!fileInfoElement) return;

    const content = fileInfoElement.querySelector('.file-info-content');
    const toggle = fileInfoElement.querySelector('.file-info-toggle');

    if (content && toggle) {
        content.classList.toggle('show');
        toggle.classList.toggle('expanded');
    }
}

// 显示消息
function showMessage(elementId, message, type = 'info') {
    const element = document.getElementById(elementId);
    element.innerHTML = `<div class="message ${type}">${message}</div>`;
    setTimeout(() => {
        element.innerHTML = '';
    }, 5000);
}

// 定义 showConversationSection 函数
function showConversationSection() {
    // 获取所有 class="section" 的元素
    const sections = document.querySelectorAll('.section');
    // 对话处理部分通常是第二个 section 元素
    // （第一个是文件上传，第二个是对话处理）
    if (sections.length > 1) {
        const conversationSection = sections[1];
        // 确保该区域可见 (虽然它默认可能是可见的)
        // 如果它有 hidden class, 需要移除
        // conversationSection.classList.remove('hidden'); 
        // 如果它是通过 style.display 控制的
        conversationSection.style.display = 'block';
    }

    const newConversationButton = document.getElementById('newConversationBtn');
    if (newConversationButton) {
        newConversationButton.style.display = 'inline-block'; // 或者 'block', 根据布局需要
    }

    // 也可以考虑显示聊天输入框等，但它们默认应该是可见的
    // const chatBox = document.getElementById('chatBox');
    // if (chatBox) chatBox.style.display = 'block';
    // const chatInputArea = document.querySelector('.fixed-bottom-chat-input');
    // if (chatInputArea) chatInputArea.style.display = 'block'; // 或者 'flex' 等
}

// 创建带思考过程的AI消息
function createAIMessageWithThinking() {
    const chatBox = document.getElementById('chatBox');
    const messageId = 'ai-message-' + Date.now();
    const messageDiv = document.createElement('div');
    messageDiv.id = messageId;
    messageDiv.className = 'chat-message assistant';
    
    // 格式化时间戳
    const timestamp = new Date().toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit' 
    });
    
    messageDiv.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: flex-start;">
            <div style="flex: 1;">
                <strong>🤖 AI助手:</strong>
                <div class="thinking-process">
                    <div class="thinking-header active" onclick="toggleThinkingProcess('${messageId}')">
                        <span class="thinking-spinner-text">💭 Thinking...</span>
                        <span class="thinking-toggle expanded">▼</span>
                    </div>
                    <div class="thinking-content show">
                        <div class="thinking-steps"></div>
                    </div>
                </div>
                <div class="ai-response-content" style="margin-top: 10px; display: none;">
                    <!-- AI回复内容将在这里显示 -->
                </div>
            </div>
            <div style="font-size: 11px; color: #888; margin-left: 10px; white-space: nowrap;">
                ${timestamp}
            </div>
        </div>
    `;
    
    chatBox.appendChild(messageDiv);
    chatBox.scrollTop = chatBox.scrollHeight;
    
    return messageId;
}

// 创建思考过程显示
function createThinkingProcess(messageId) {
    const messageElement = document.getElementById(messageId);
    if (!messageElement) return;
    
    const thinkingDiv = document.createElement('div');
    thinkingDiv.className = 'thinking-process';
    thinkingDiv.innerHTML = `
        <div class="thinking-header" onclick="toggleThinkingProcess('${messageId}')">
            <span class="thinking-spinner-text">💭 Thinking...</span>
            <span class="thinking-toggle">▼</span>
        </div>
        <div class="thinking-content show">
            <div class="thinking-steps"></div>
        </div>
    `;
    
    // 插入到AI消息内容之前
    const responseContent = messageElement.querySelector('.ai-response-content');
    messageElement.insertBefore(thinkingDiv, responseContent);
    
    return thinkingDiv;
}

// 添加思考步骤
function addThinkingStep(messageId, stepId, status, message, details = null) {
    const stepsContainer = document.querySelector(`#${messageId} .thinking-steps`);
    const stepDiv = document.createElement('div');
    stepDiv.className = `thinking-step ${status.toLowerCase()}`;
    stepDiv.setAttribute('data-step', stepId);
    
    const stepIcon = document.createElement('span');
    stepIcon.className = 'thinking-step-icon';
    stepIcon.textContent = stepId.charAt(0).toUpperCase();
    
    const stepContent = document.createElement('span');
    stepContent.className = 'thinking-step-content';
    stepContent.textContent = message;
    
    if (details) {
        const codePreview = document.createElement('div');
        codePreview.className = 'thinking-code-preview';
        codePreview.textContent = details;
        stepContent.appendChild(codePreview);
    }
    
    stepDiv.appendChild(stepIcon);
    stepDiv.appendChild(stepContent);
    
    stepsContainer.appendChild(stepDiv);
}

// 更新思考步骤状态
function updateThinkingStep(messageId, stepId, status, message, details = null) {
    const step = document.querySelector(`#${messageId} .thinking-step[data-step="${stepId}"]`);
    if (step) {
        step.className = `thinking-step ${status.toLowerCase()}`;
        step.innerHTML = `
            <span class="thinking-step-icon">${stepId.charAt(0).toUpperCase()}</span>
            <span class="thinking-step-content">${message}</span>
        `;
        
        if (details) {
            const codePreview = step.querySelector('.thinking-code-preview');
            if (codePreview) {
                codePreview.textContent = details;
            } else {
                const newCodePreview = document.createElement('div');
                newCodePreview.className = 'thinking-code-preview';
                newCodePreview.textContent = details;
                step.appendChild(newCodePreview);
            }
        }
    }
}

// 完成思考过程并自动折叠
function completeThinkingProcess(messageId, duration) {
    const messageElement = document.getElementById(messageId);
    if (messageElement) {
        const thinkingDiv = messageElement.querySelector('.thinking-process');
        if (thinkingDiv) {
            thinkingDiv.classList.add('completed');
            const thinkingHeader = thinkingDiv.querySelector('.thinking-header');
            if (thinkingHeader) {
                thinkingHeader.innerHTML = `
                    <span class="thinking-spinner-text">💭 Thinking...</span>
                    <span class="thinking-toggle">▼</span>
                `;
            }
        }
    }
    
    const thinkingContent = messageElement.querySelector('.thinking-content');
    if (thinkingContent) {
        thinkingContent.classList.add('completed');
    }
    
    const thinkingSteps = messageElement.querySelectorAll('.thinking-step');
    thinkingSteps.forEach(step => step.classList.add('completed'));
    
    const thinkingHeader = messageElement.querySelector('.thinking-header');
    if (thinkingHeader) {
        thinkingHeader.classList.add('completed');
    }
    
    const thinkingToggle = messageElement.querySelector('.thinking-toggle');
    if (thinkingToggle) {
        thinkingToggle.classList.add('completed');
    }
    
    const thinkingTime = messageElement.querySelector('.thinking-time');
    if (thinkingTime) {
        thinkingTime.textContent = `完成时间: ${duration}秒`;
    }
}

// 更新AI消息内容
function updateAIMessageContent(messageId, content) {
    const messageElement = document.getElementById(messageId);
    if (messageElement) {
        const aiResponseContent = messageElement.querySelector('.ai-response-content');
        if (aiResponseContent) {
            aiResponseContent.innerHTML = content;
            aiResponseContent.style.display = 'block';
        }
    }
}

// 添加消息到聊天框
function addMessageToChat(sender, message) {
    const chatBox = document.getElementById('chatBox');
    const messageDiv = document.createElement('div');
    messageDiv.className = `chat-message ${sender}`;
    
    // 格式化时间戳
    const timestamp = new Date().toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit' 
    });
    
    let senderLabel = '';
    if (sender === 'user') {
        senderLabel = '👤 您:';
    } else if (sender === 'assistant') {
        senderLabel = '🤖 AI助手:';
    } else if (sender === 'system') {
        senderLabel = '🔔 系统:';
    } else {
        senderLabel = '🤖 AI助手:';
    }

    messageDiv.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: flex-start;">
            <div style="flex: 1;">
                <strong>${senderLabel}</strong>
                <div class="message">${message}</div>
            </div>
            <div style="font-size: 11px; color: #888; margin-left: 10px; white-space: nowrap;">
                ${timestamp}
            </div>
        </div>
    `;
    
    chatBox.appendChild(messageDiv);
    chatBox.scrollTop = chatBox.scrollHeight;
}

// 切换文件信息显示/隐藏
function toggleFileInfo() {
    const content = document.getElementById('fileInfoContent');
    const toggle = document.querySelector('.file-info-toggle');

    if (content && toggle) {
        if (content.classList.contains('show')) {
            content.classList.remove('show');
            toggle.classList.add('collapsed');
        } else {
            content.classList.add('show');
            toggle.classList.remove('collapsed');
        }
    }
}

// 切换思考过程显示/隐藏
function toggleThinkingProcess(messageId) {
    const messageElement = document.getElementById(messageId);
    if (!messageElement) return;

    const thinkingContent = messageElement.querySelector('.thinking-content');
    const thinkingToggle = messageElement.querySelector('.thinking-toggle');
    const thinkingHeader = messageElement.querySelector('.thinking-header');

    if (thinkingContent && thinkingToggle) {
        if (thinkingContent.classList.contains('show')) {
            thinkingContent.classList.remove('show');
            thinkingToggle.classList.remove('expanded');
            thinkingHeader.classList.remove('active');
        } else {
            thinkingContent.classList.add('show');
            thinkingToggle.classList.add('expanded');
            thinkingHeader.classList.add('active');
        }
    }
}
