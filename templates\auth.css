/* 认证页面样式 */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.auth-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 400px;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h1 {
    color: #333;
    margin-bottom: 8px;
    font-size: 28px;
    font-weight: 600;
}

.auth-header p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 500;
    color: #333;
    margin-bottom: 6px;
    font-size: 14px;
}

.form-group input {
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
    background: #fafafa;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
}

.form-group input:invalid {
    border-color: #e74c3c;
}

.auth-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 14px 20px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
    overflow: hidden;
}

.auth-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.auth-button:active {
    transform: translateY(0);
}

.auth-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.loading-spinner {
    display: none;
}

.auth-button.loading .button-text {
    display: none;
}

.auth-button.loading .loading-spinner {
    display: inline;
}

.error-message {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 4px;
    min-height: 16px;
}

.success-message {
    color: #27ae60;
    font-size: 12px;
    margin-top: 4px;
    min-height: 16px;
}

.help-text {
    color: #7f8c8d;
    font-size: 11px;
    margin-top: 4px;
}

.auth-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.auth-footer p {
    margin: 8px 0;
    font-size: 14px;
    color: #666;
}

.auth-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* 用户状态显示 */
.user-status {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 15px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.user-info {
    flex: 1;
}

.user-info .nickname {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.user-info .quota {
    font-size: 12px;
    color: #666;
}

.logout-btn {
    background: none;
    border: 1px solid #ddd;
    color: #666;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.logout-btn:hover {
    background: #f5f5f5;
    border-color: #ccc;
}

/* 试用状态显示 */
.trial-status {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 12px 15px;
    margin-bottom: 15px;
}

.trial-status .title {
    font-weight: 500;
    color: #856404;
    font-size: 14px;
    margin-bottom: 4px;
}

.trial-status .message {
    font-size: 12px;
    color: #856404;
}

.trial-status .register-prompt {
    margin-top: 8px;
}

.trial-status .register-prompt a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.trial-status .register-prompt a:hover {
    text-decoration: underline;
}
