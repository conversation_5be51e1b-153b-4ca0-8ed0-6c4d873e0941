"""
认证工具函数
包含JWT生成验证、密码加密等功能
"""
import os
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from passlib.context import CryptContext
from jose import JWTError, jwt
import re

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT配置
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-this-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_HOURS = 24

# 试用和消息限制配置
TRIAL_LIMIT = int(os.getenv("TRIAL_LIMIT", "3"))
DAILY_MESSAGE_LIMIT = int(os.getenv("DAILY_MESSAGE_LIMIT", "30"))

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return pwd_context.hash(password)

def validate_password_strength(password: str) -> tuple[bool, str]:
    """验证密码强度"""
    if len(password) < 8:
        return False, "密码长度至少8位"
    
    if not re.search(r"[a-zA-Z]", password):
        return False, "密码必须包含字母"
    
    if not re.search(r"\d", password):
        return False, "密码必须包含数字"
    
    return True, "密码强度符合要求"

def validate_username(username: str) -> tuple[bool, str]:
    """验证用户名格式"""
    if len(username) < 3:
        return False, "用户名长度至少3位"
    
    if len(username) > 20:
        return False, "用户名长度不能超过20位"
    
    if not re.match(r"^[a-zA-Z0-9_]+$", username):
        return False, "用户名只能包含字母、数字和下划线"
    
    return True, "用户名格式正确"

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """创建JWT访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=ACCESS_TOKEN_EXPIRE_HOURS)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """验证JWT令牌"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None

def generate_trial_cookie() -> str:
    """生成试用cookie"""
    return str(uuid.uuid4())

def extract_token_from_header(authorization: str) -> Optional[str]:
    """从Authorization头中提取token"""
    if not authorization:
        return None
    
    parts = authorization.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        return None
    
    return parts[1]
