"""
数据库模型定义
包含用户、消息记录、试用记录等表的SQLAlchemy模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Date
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime, date

Base = declarative_base()

class User(Base):
    """用户表"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    nickname = Column(String(100), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # 关联消息记录
    message_records = relationship("MessageRecord", back_populates="user")

class MessageRecord(Base):
    """消息记录表"""
    __tablename__ = "message_records"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # 可为空，支持未登录用户
    trial_cookie = Column(String(255), nullable=True)  # 试用用户的cookie
    message_content = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    date = Column(Date, default=date.today)  # 用于统计每日发送量
    
    # 关联用户
    user = relationship("User", back_populates="message_records")

class TrialRecord(Base):
    """试用记录表"""
    __tablename__ = "trial_records"
    
    id = Column(Integer, primary_key=True, index=True)
    cookie_value = Column(String(255), unique=True, index=True, nullable=False)
    usage_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_used_at = Column(DateTime, default=datetime.utcnow)
