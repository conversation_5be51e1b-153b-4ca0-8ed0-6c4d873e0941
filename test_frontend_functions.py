#!/usr/bin/env python3
"""
测试前端功能修复的脚本
验证各种按钮和功能是否正常工作
"""

import requests
import json
import time
import os

BASE_URL = "http://localhost:63225"

def test_server_status():
    """测试服务器是否正常运行"""
    try:
        response = requests.get(BASE_URL)
        if response.status_code == 200:
            print("✅ 服务器正常运行")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False

def test_config_status():
    """测试配置状态接口"""
    try:
        response = requests.get(f"{BASE_URL}/config/status")
        if response.status_code == 200:
            result = response.json()
            print("✅ 配置状态接口正常")
            print(f"   - 环境配置: {'有' if result.get('has_env_config') else '无'}")
            print(f"   - 手动配置: {'有' if result.get('has_manual_config') else '无'}")
            return True
        else:
            print(f"❌ 配置状态接口异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 配置状态接口错误: {e}")
        return False

def test_conversations_api():
    """测试对话历史接口"""
    try:
        response = requests.get(f"{BASE_URL}/conversations")
        if response.status_code == 200:
            result = response.json()
            print("✅ 对话历史接口正常")
            print(f"   - 对话数量: {len(result.get('conversations', []))}")
            return True
        else:
            print(f"❌ 对话历史接口异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 对话历史接口错误: {e}")
        return False

def test_file_upload():
    """测试文件上传功能"""
    # 检查测试文件是否存在
    test_file_path = "test_files/销售数据测试.xlsx"
    if not os.path.exists(test_file_path):
        print(f"❌ 测试文件不存在: {test_file_path}")
        return False
    
    try:
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = requests.post(f"{BASE_URL}/upload", files=files)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 文件上传功能正常")
                print(f"   - 文件ID: {result.get('file_id')}")
                print(f"   - 文件名: {result.get('filename')}")
                return result.get('file_id')
            else:
                print(f"❌ 文件上传失败: {result}")
                return False
        else:
            print(f"❌ 文件上传接口异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 文件上传错误: {e}")
        return False

def test_new_conversation(file_id):
    """测试创建新对话功能"""
    if not file_id:
        print("❌ 无法测试创建新对话：缺少文件ID")
        return False
    
    try:
        data = {"file_id": file_id}
        response = requests.post(f"{BASE_URL}/conversations/new", 
                               json=data,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 创建新对话功能正常")
                print(f"   - 对话ID: {result.get('conversation_id')}")
                return result.get('conversation_id')
            else:
                print(f"❌ 创建新对话失败: {result}")
                return False
        else:
            print(f"❌ 创建新对话接口异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 创建新对话错误: {e}")
        return False

def test_conversation_detail(conversation_id):
    """测试获取对话详情功能"""
    if not conversation_id:
        print("❌ 无法测试对话详情：缺少对话ID")
        return False
    
    try:
        response = requests.get(f"{BASE_URL}/conversations/{conversation_id}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 获取对话详情功能正常")
                conv = result.get('conversation', {})
                print(f"   - 原始文件名: {conv.get('original_filename')}")
                print(f"   - 消息数量: {len(conv.get('messages', []))}")
                return True
            else:
                print(f"❌ 获取对话详情失败: {result}")
                return False
        else:
            print(f"❌ 获取对话详情接口异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取对话详情错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试前端功能修复...")
    print("=" * 50)
    
    # 测试服务器状态
    if not test_server_status():
        print("❌ 服务器未运行，无法继续测试")
        return
    
    print()
    
    # 测试配置状态
    test_config_status()
    print()
    
    # 测试对话历史接口
    test_conversations_api()
    print()
    
    # 测试文件上传
    file_id = test_file_upload()
    print()
    
    # 测试创建新对话
    conversation_id = test_new_conversation(file_id)
    print()
    
    # 测试获取对话详情
    test_conversation_detail(conversation_id)
    print()
    
    print("=" * 50)
    print("🎉 测试完成！")
    print()
    print("📋 修复总结:")
    print("1. ✅ 实现了 createNewConversation() 函数")
    print("2. ✅ 实现了 loadConversations() 函数")
    print("3. ✅ 实现了 toggleThinkingProcess() 函数")
    print("4. ✅ 实现了 loadConversation() 函数")
    print("5. ✅ 实现了 viewConversation() 函数")
    print("6. ✅ 实现了 deleteConversation() 函数")
    print("7. ✅ 修复了 addMessageToChat() 函数支持系统消息")
    print("8. ✅ 修复了JavaScript变量重复声明问题")
    print()
    print("🌐 请在浏览器中访问 http://localhost:63225 测试界面功能")

if __name__ == "__main__":
    main()
