#!/usr/bin/env python3
"""
用户管理系统测试脚本
测试注册、登录、试用限制、消息配额等功能
"""
import requests
import json
import time
import sys
import os

# 测试配置
BASE_URL = "http://localhost:63225"
TEST_USERNAME = "testuser123"
TEST_PASSWORD = "testpass123"
TEST_NICKNAME = "测试用户"

class UserSystemTester:
    def __init__(self):
        self.session = requests.Session()
        self.auth_token = None
        
    def test_user_registration(self):
        """测试用户注册"""
        print("🧪 测试用户注册...")
        
        # 测试注册
        register_data = {
            "username": TEST_USERNAME,
            "password": TEST_PASSWORD,
            "nickname": TEST_NICKNAME
        }
        
        response = self.session.post(f"{BASE_URL}/auth/register", json=register_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ 用户注册成功")
                return True
            else:
                print(f"❌ 注册失败: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 注册请求失败: {response.status_code}")
            return False
    
    def test_user_login(self):
        """测试用户登录"""
        print("🧪 测试用户登录...")
        
        login_data = {
            "username": TEST_USERNAME,
            "password": TEST_PASSWORD
        }
        
        response = self.session.post(f"{BASE_URL}/auth/login", json=login_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("access_token"):
                self.auth_token = result["access_token"]
                print("✅ 用户登录成功")
                print(f"   用户信息: {result['user']['nickname']} ({result['user']['username']})")
                return True
            else:
                print("❌ 登录失败: 未获取到token")
                return False
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            return False
    
    def test_trial_system(self):
        """测试试用系统"""
        print("🧪 测试试用系统...")
        
        # 清除认证信息，模拟未登录用户
        old_token = self.auth_token
        self.auth_token = None
        
        # 初始化试用
        response = self.session.post(f"{BASE_URL}/trial/init")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ 试用初始化成功")
                print(f"   试用次数: {result['usage_count']}/{result['usage_count'] + result['remaining_count']}")
                
                # 检查试用状态
                check_response = self.session.get(f"{BASE_URL}/trial/check")
                if check_response.status_code == 200:
                    check_result = check_response.json()
                    print(f"✅ 试用状态检查成功: 剩余 {check_result['remaining_count']} 次")
                else:
                    print("❌ 试用状态检查失败")
                    
                # 恢复认证信息
                self.auth_token = old_token
                return True
            else:
                print("❌ 试用初始化失败")
                self.auth_token = old_token
                return False
        else:
            print(f"❌ 试用初始化请求失败: {response.status_code}")
            self.auth_token = old_token
            return False
    
    def test_auth_required_endpoint(self):
        """测试需要认证的端点"""
        print("🧪 测试认证端点...")
        
        if not self.auth_token:
            print("❌ 没有认证token，跳过测试")
            return False
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        response = self.session.get(f"{BASE_URL}/auth/me", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 认证端点访问成功")
            print(f"   当前用户: {result['nickname']} ({result['username']})")
            return True
        else:
            print(f"❌ 认证端点访问失败: {response.status_code}")
            return False
    
    def test_database_connection(self):
        """测试数据库连接"""
        print("🧪 测试数据库连接...")
        
        try:
            # 导入数据库模块
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from database.database import SessionLocal, engine
            from database.models import User
            
            # 测试数据库连接
            db = SessionLocal()
            try:
                # 查询用户数量
                user_count = db.query(User).count()
                print(f"✅ 数据库连接成功，当前用户数: {user_count}")
                return True
            finally:
                db.close()
                
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def cleanup_test_user(self):
        """清理测试用户"""
        print("🧹 清理测试数据...")
        
        try:
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from database.database import SessionLocal
            from database.models import User, MessageRecord, TrialRecord
            
            db = SessionLocal()
            try:
                # 删除测试用户及相关数据
                test_user = db.query(User).filter(User.username == TEST_USERNAME).first()
                if test_user:
                    # 删除消息记录
                    db.query(MessageRecord).filter(MessageRecord.user_id == test_user.id).delete()
                    # 删除用户
                    db.delete(test_user)
                    db.commit()
                    print("✅ 测试用户数据清理完成")
                else:
                    print("ℹ️ 未找到测试用户，无需清理")
                    
            finally:
                db.close()
                
        except Exception as e:
            print(f"⚠️ 清理测试数据时出错: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始用户管理系统测试\n")
        
        tests = [
            ("数据库连接", self.test_database_connection),
            ("用户注册", self.test_user_registration),
            ("用户登录", self.test_user_login),
            ("认证端点", self.test_auth_required_endpoint),
            ("试用系统", self.test_trial_system),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n--- {test_name} ---")
            try:
                if test_func():
                    passed += 1
                time.sleep(0.5)  # 短暂延迟
            except Exception as e:
                print(f"❌ 测试 {test_name} 时发生异常: {e}")
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        # 清理测试数据
        self.cleanup_test_user()
        
        return passed == total

if __name__ == "__main__":
    tester = UserSystemTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
