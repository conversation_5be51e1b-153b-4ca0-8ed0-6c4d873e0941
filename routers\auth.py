"""
用户认证路由
包含注册、登录、登出等API
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from database.database import get_db
from database.crud import get_user_by_username, create_user
from database.models import User
from models.schemas import UserRegister, UserLogin, Token, UserResponse
from auth.auth import (
    verify_password, get_password_hash, create_access_token,
    validate_password_strength, validate_username
)
from auth.dependencies import get_current_user_required

router = APIRouter(prefix="/auth", tags=["认证"])

@router.post("/register", response_model=dict)
async def register(user_data: UserRegister, db: Session = Depends(get_db)):
    """用户注册"""
    try:
        # 验证用户名格式
        username_valid, username_msg = validate_username(user_data.username)
        if not username_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=username_msg
            )
        
        # 验证密码强度
        password_valid, password_msg = validate_password_strength(user_data.password)
        if not password_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=password_msg
            )
        
        # 检查用户名是否已存在
        existing_user = get_user_by_username(db, user_data.username)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        # 创建新用户
        password_hash = get_password_hash(user_data.password)
        new_user = create_user(
            db=db,
            username=user_data.username,
            password_hash=password_hash,
            nickname=user_data.nickname
        )
        
        return {
            "success": True,
            "message": "注册成功",
            "user_id": new_user.id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"注册失败: {str(e)}"
        )

@router.post("/login", response_model=Token)
async def login(user_data: UserLogin, db: Session = Depends(get_db)):
    """用户登录"""
    try:
        # 查找用户
        user = get_user_by_username(db, user_data.username)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 验证密码
        if not verify_password(user_data.password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 检查用户是否激活
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="账号已被禁用"
            )
        
        # 生成访问令牌
        access_token = create_access_token(data={"sub": str(user.id)})
        
        # 构建用户信息
        user_response = UserResponse(
            id=user.id,
            username=user.username,
            nickname=user.nickname,
            created_at=user.created_at,
            is_active=user.is_active
        )
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            user=user_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )

@router.post("/logout")
async def logout():
    """用户登出"""
    # JWT是无状态的，客户端删除token即可
    return {"success": True, "message": "登出成功"}

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user_required)):
    """获取当前用户信息"""
    return UserResponse(
        id=current_user.id,
        username=current_user.username,
        nickname=current_user.nickname,
        created_at=current_user.created_at,
        is_active=current_user.is_active
    )
