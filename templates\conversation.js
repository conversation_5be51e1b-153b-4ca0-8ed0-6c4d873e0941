/**
 * 对话历史管理功能
 * 包含对话的创建、加载、查看、删除等功能
 */

// 创建新对话
async function createNewConversation() {
    if (!currentFileId) {
        showMessage('chatMessage', '请先上传Excel文件', 'error');
        return;
    }

    try {
        const response = await fetch('/conversations/new', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                file_id: currentFileId
            })
        });

        const result = await response.json();
        if (result.success) {
            currentConversationId = result.conversation_id;
            showMessage('chatMessage', '新对话已创建！', 'success');

            // 清空聊天框
            document.getElementById('chatBox').innerHTML = '';

            // 添加提示消息
            addMessageToChat('system', '🆕 新对话已创建，您可以开始与AI对话处理Excel文件了。');
        } else {
            showMessage('chatMessage', result.message || '创建对话失败', 'error');
        }
    } catch (error) {
        showMessage('chatMessage', '创建对话失败: ' + error.message, 'error');
    }
}

// 加载对话历史
async function loadConversations() {
    const conversationsList = document.getElementById('conversationsList');
    conversationsList.innerHTML = '<p>正在加载对话历史...</p>';

    try {
        const response = await fetch('/conversations');
        const result = await response.json();

        if (result.success) {
            if (result.conversations.length === 0) {
                conversationsList.innerHTML = '<div class="empty-conversations">暂无对话历史</div>';
                return;
            }

            let html = '';
            result.conversations.forEach(conv => {
                const isCurrentConv = conv.id === currentConversationId;
                html += `
                    <div class="conversation-card">
                        <div class="conversation-header">
                            <div class="conversation-title">
                                📄 ${conv.original_filename}
                                ${isCurrentConv ? '<span class="current-conversation-badge">当前对话</span>' : ''}
                            </div>
                        </div>
                        <div class="conversation-meta">
                            创建时间: ${new Date(conv.created_at).toLocaleString('zh-CN')} |
                            消息数: ${conv.message_count}
                        </div>
                        <div class="conversation-actions">
                            <button class="btn-small btn-primary" onclick="loadConversation('${conv.id}')">
                                💬 继续对话
                            </button>
                            <button class="btn-small btn-secondary" onclick="viewConversation('${conv.id}')">
                                👁️ 查看详情
                            </button>
                            <button class="btn-small btn-warning" onclick="deleteConversation('${conv.id}')">
                                🗑️ 删除
                            </button>
                        </div>
                    </div>
                `;
            });

            conversationsList.innerHTML = html;
        } else {
            conversationsList.innerHTML = '<div class="message error">加载对话历史失败: ' + result.message + '</div>';
        }
    } catch (error) {
        conversationsList.innerHTML = '<div class="message error">加载对话历史失败: ' + error.message + '</div>';
    }
}

// 加载特定对话
async function loadConversation(conversationId) {
    try {
        const response = await fetch(`/conversations/${conversationId}`);
        const result = await response.json();

        if (result.success) {
            currentConversationId = conversationId;

            // 切换到主页面
            switchTab('main');

            // 清空聊天框
            const chatBox = document.getElementById('chatBox');
            chatBox.innerHTML = '';

            // 显示对话历史
            result.conversation.messages.forEach(msg => {
                addMessageToChat(msg.role === 'user' ? 'user' : 'assistant', msg.content);
            });

            showMessage('chatMessage', '对话已加载，您可以继续对话', 'success');
        } else {
            showMessage('chatMessage', '加载对话失败: ' + result.message, 'error');
        }
    } catch (error) {
        showMessage('chatMessage', '加载对话失败: ' + error.message, 'error');
    }
}

// 查看对话详情
async function viewConversation(conversationId) {
    try {
        const response = await fetch(`/conversations/${conversationId}`);
        const result = await response.json();

        if (result.success) {
            const conv = result.conversation;
            alert(`对话详情:\n\n文件: ${conv.original_filename}\n创建时间: ${new Date(conv.created_at).toLocaleString('zh-CN')}\n消息数: ${conv.messages.length}`);
        } else {
            alert('获取对话详情失败: ' + result.message);
        }
    } catch (error) {
        alert('获取对话详情失败: ' + error.message);
    }
}

// 删除对话
async function deleteConversation(conversationId) {
    if (!confirm('确定要删除这个对话吗？此操作不可撤销。')) {
        return;
    }

    try {
        const response = await fetch(`/conversations/${conversationId}`, {
            method: 'DELETE'
        });

        const result = await response.json();
        if (result.success) {
            // 如果删除的是当前对话，清空当前对话ID
            if (conversationId === currentConversationId) {
                currentConversationId = null;
                document.getElementById('chatBox').innerHTML = '';
            }

            // 重新加载对话列表
            loadConversations();

            showMessage('chatMessage', '对话已删除', 'success');
        } else {
            alert('删除对话失败: ' + result.message);
        }
    } catch (error) {
        alert('删除对话失败: ' + error.message);
    }
}
