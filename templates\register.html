<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - Excel对话编辑系统</title>
    <link rel="stylesheet" href="/static/styles.css">
    <link rel="stylesheet" href="/static/auth.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>用户注册</h1>
                <p>创建您的账号，享受每日30条消息的完整服务</p>
            </div>
            
            <form id="registerForm" class="auth-form">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required 
                           placeholder="3-20位字母、数字或下划线" autocomplete="username">
                    <div class="error-message" id="usernameError"></div>
                    <div class="help-text">用户名长度3-20位，只能包含字母、数字和下划线</div>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required 
                           placeholder="至少8位，包含字母和数字" autocomplete="new-password">
                    <div class="error-message" id="passwordError"></div>
                    <div class="help-text">密码长度至少8位，必须包含字母和数字</div>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">确认密码</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required 
                           placeholder="请再次输入密码" autocomplete="new-password">
                    <div class="error-message" id="confirmPasswordError"></div>
                </div>
                
                <div class="form-group">
                    <label for="nickname">昵称</label>
                    <input type="text" id="nickname" name="nickname" required 
                           placeholder="请输入您的昵称" autocomplete="nickname">
                    <div class="error-message" id="nicknameError"></div>
                    <div class="help-text">昵称长度1-50位</div>
                </div>
                
                <div class="form-group">
                    <button type="submit" id="registerBtn" class="auth-button">
                        <span class="button-text">注册</span>
                        <span class="loading-spinner" style="display: none;">注册中...</span>
                    </button>
                </div>
                
                <div class="error-message" id="generalError"></div>
                <div class="success-message" id="successMessage"></div>
            </form>
            
            <div class="auth-footer">
                <p>已有账号？ <a href="/login">立即登录</a></p>
                <p><a href="/">返回主页</a></p>
            </div>
        </div>
    </div>
    
    <script src="/static/auth.js"></script>
    <script>
        // 页面加载完成后初始化注册功能
        document.addEventListener('DOMContentLoaded', function() {
            initRegisterPage();
        });
    </script>
</body>
</html>
