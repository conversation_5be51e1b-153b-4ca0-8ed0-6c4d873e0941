"""
数据模型定义
包含所有Pydantic数据模型
"""
from pydantic import BaseModel, validator
from typing import Optional, List, Dict
from datetime import datetime

class OpenAIConfig(BaseModel):
    """OpenAI配置模型"""
    api_url: str
    api_key: str
    model_name: str

class ChatMessage(BaseModel):
    """聊天消息模型"""
    message: str
    file_id: str
    conversation_id: Optional[str] = None

class ConversationSession(BaseModel):
    """对话会话模型"""
    id: str
    file_id: str
    original_filename: str
    created_at: datetime
    messages: List[Dict[str, str]]
    current_file_path: str  # 当前会话使用的文件路径

class NewConversationRequest(BaseModel):
    """新建对话请求模型"""
    file_id: str

class FileUploadResponse(BaseModel):
    """文件上传响应模型"""
    success: bool
    file_id: str
    filename: str
    sheet_info: Dict

class ChatResponse(BaseModel):
    """聊天响应模型"""
    success: bool
    type: str  # "code_execution" 或 "direct_answer"
    message: str
    conversation_id: Optional[str] = None
    generated_code: Optional[str] = None
    execution_output: Optional[str] = None
    download_id: Optional[str] = None
    download_url: Optional[str] = None
    file_modified: Optional[bool] = None

class ConfigStatusResponse(BaseModel):
    """配置状态响应模型"""
    success: bool
    has_env_config: bool
    has_manual_config: bool
    current_config: Dict
    env_config: Dict

# 用户认证相关模型
class UserRegister(BaseModel):
    """用户注册模型"""
    username: str
    password: str
    nickname: str

    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3 or len(v) > 20:
            raise ValueError('用户名长度必须在3-20位之间')
        return v

    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('密码长度至少8位')
        return v

    @validator('nickname')
    def validate_nickname(cls, v):
        if len(v) < 1 or len(v) > 50:
            raise ValueError('昵称长度必须在1-50位之间')
        return v

class UserLogin(BaseModel):
    """用户登录模型"""
    username: str
    password: str

class UserResponse(BaseModel):
    """用户信息响应模型"""
    id: int
    username: str
    nickname: str
    created_at: datetime
    is_active: bool

class Token(BaseModel):
    """令牌响应模型"""
    access_token: str
    token_type: str
    user: UserResponse

class TrialCheckResponse(BaseModel):
    """试用检查响应模型"""
    success: bool
    trial_cookie: str
    usage_count: int
    remaining_count: int
    limit_reached: bool

class MessageQuotaResponse(BaseModel):
    """消息配额响应模型"""
    success: bool
    remaining_quota: int
    quota_type: str  # "trial" 或 "daily"
    message: str