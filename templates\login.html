<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - Excel对话编辑系统</title>
    <link rel="stylesheet" href="/static/styles.css">
    <link rel="stylesheet" href="/static/auth.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>用户登录</h1>
                <p>登录您的账号以享受完整功能</p>
            </div>
            
            <form id="loginForm" class="auth-form">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required 
                           placeholder="请输入用户名" autocomplete="username">
                    <div class="error-message" id="usernameError"></div>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required 
                           placeholder="请输入密码" autocomplete="current-password">
                    <div class="error-message" id="passwordError"></div>
                </div>
                
                <div class="form-group">
                    <button type="submit" id="loginBtn" class="auth-button">
                        <span class="button-text">登录</span>
                        <span class="loading-spinner" style="display: none;">登录中...</span>
                    </button>
                </div>
                
                <div class="error-message" id="generalError"></div>
                <div class="success-message" id="successMessage"></div>
            </form>
            
            <div class="auth-footer">
                <p>还没有账号？ <a href="/register">立即注册</a></p>
                <p><a href="/">返回主页</a></p>
            </div>
        </div>
    </div>
    
    <script src="/static/auth.js"></script>
    <script>
        // 页面加载完成后初始化登录功能
        document.addEventListener('DOMContentLoaded', function() {
            initLoginPage();
        });
    </script>
</body>
</html>
