<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel对话编辑系统</title>
    <!-- 引入外部CSS文件 -->
    <link rel="stylesheet" href="/static/styles.css">
    <link rel="stylesheet" href="/static/auth.css">
    <!-- 引入marked.js用于markdown解析 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>Excel对话编辑系统</h1>

        <!-- 用户状态区域 -->
        <div id="userStatusArea" class="user-status-area">
            <!-- 登录用户状态 -->
            <div id="loggedInStatus" class="user-status" style="display: none;">
                <div class="user-avatar" id="userAvatar"></div>
                <div class="user-info">
                    <div class="nickname" id="userNickname"></div>
                    <div class="quota" id="userQuota"></div>
                </div>
                <button class="logout-btn" onclick="handleLogout()">登出</button>
            </div>

            <!-- 试用用户状态 -->
            <div id="trialStatus" class="trial-status" style="display: none;">
                <div class="title">试用模式</div>
                <div class="message" id="trialMessage"></div>
                <div class="register-prompt">
                    <a href="/register">注册账号</a> 享受每日30条消息的完整服务
                </div>
            </div>

            <!-- 未登录状态 -->
            <div id="notLoggedInStatus" class="auth-prompt" style="display: none;">
                <div class="auth-buttons">
                    <a href="/login" class="auth-btn login-btn">登录</a>
                    <a href="/register" class="auth-btn register-btn">注册</a>
                </div>
                <div class="trial-info">
                    或继续试用（剩余 <span id="trialCount">3</span> 次）
                </div>
            </div>
        </div>

        <!-- Tab导航 -->
        <div class="tab-container">
            <div class="tab-buttons">
                <button class="tab-button active" onclick="switchTab('main')">🏠 主页面</button>
                <button class="tab-button" onclick="switchTab('conversations')">💬 对话历史</button>
                <!-- <button class="tab-button" onclick="switchTab('config')">⚙️ API配置</button> -->
            </div>
            
            <!-- 主页面Tab -->
            <div id="main-tab" class="tab-content active">
                <!-- 文件上传部分 -->
                <div class="section">
                    <div class="section-header">📤 上传Excel文件</div>
                    <div class="section-content">
                        <input type="file" id="excelFile" accept=".xlsx,.xls" style="display: none;">
                        <button onclick="selectAndUploadFile()" class="btn-upload">选择并上传Excel文件</button>
                        <div id="uploadMessage"></div>
                        <div id="fileInfo" class="file-info hidden">
                            <div class="file-info-header" onclick="toggleFileInfo('fileInfo')">
                                <span>📄 文件信息</span>
                                <span class="file-info-toggle">▼</span>
                            </div>
                            <div class="file-info-content">
                                <div id="fileInfoContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 对话处理部分 -->
                <div class="section">
                    <div class="section-header">
                        <span>💬 与AI对话处理Excel</span>
                        <button id="newConversationBtn" onclick="createNewConversation()" class="header-button" style="display: none;">🆕 创建新对话</button>
                    </div>
                    <div class="section-content">
                        <!-- 删除对话上下文提示区域 -->
                        
                        <div id="chatBox" class="chat-box"></div>
                        
                        <div class="fixed-bottom-chat-input">
                            <div id="chatMessage" style="padding: 5px 20px;"></div>
                            <div class="chat-input-container">
                                <div class="chat-input-wrapper">
                                    <textarea id="userMessage" rows="4" placeholder="告诉我你的需求~" class="chat-textarea"></textarea>
                                    <button onclick="sendMessage()" id="sendBtn" class="send-button">
                                        <span class="send-arrow"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 对话历史Tab -->
            <div id="conversations-tab" class="tab-content">
                <div class="section">
                    <div class="section-header">📚 对话历史管理</div>
                    <div class="section-content">
                        <div style="margin-bottom: 15px;">
                            <button onclick="loadConversations()" class="btn-success">🔄 刷新列表</button>
                        </div>
                        <div id="conversationsList">
                            <p>正在加载对话历史...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 配置Tab - 隐藏 -->
            <div id="config-tab" class="tab-content" style="display: none;">
                <div class="section">
                    <div class="section-header">🔧 OpenAI API 配置</div>
                    <div class="section-content">
                        <div class="grid">
                            <div>
                                <div class="form-group">
                                    <label for="apiUrl">API URL:</label>
                                    <input type="text" id="apiUrl" placeholder="例如: https://api.openai.com/v1/chat/completions">
                                </div>
                                <div class="form-group">
                                    <label for="apiKey">API Key:</label>
                                    <input type="password" id="apiKey" placeholder="sk-...">
                                </div>
                                <div class="form-group">
                                    <label for="modelName">模型名称:</label>
                                    <input type="text" id="modelName" value="gpt-3.5-turbo" placeholder="gpt-3.5-turbo">
                                </div>
                            </div>
                            <div>
                                <div style="padding-top: 20px;">
                                    <button onclick="loadEnvConfig()" style="margin-right: 10px; background: #f39c12;">从.env加载配置</button>
                                    <button onclick="saveConfig()" style="margin-right: 10px;">保存配置</button>
                                    <button onclick="testConnection()" class="btn-success">测试连接</button>
                                </div>
                                <div id="configMessage"></div>
                                <div id="configStatus" style="margin-top: 10px; font-size: 12px; color: #666;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="/static/auth.js"></script>
    <script src="/static/ui-utils.js"></script>
    <script src="/static/conversation.js"></script>
    <script src="/static/main.js"></script>
</body>
</html>
