"""
聊天路由
处理与AI的对话，集成用户验证和消息限制
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any
import os
import uuid
import shutil
import traceback
from datetime import datetime

from database.database import get_db
from database.crud import create_message_record
from models.schemas import ChatMessage, ChatResponse
from auth.dependencies import check_message_quota
from services.excel_service import ExcelService
from services.ai_service import AIService
from config.settings import settings

router = APIRouter(tags=["聊天"])

@router.post("/chat", response_model=dict)
async def chat_with_ai(
    chat_data: ChatMessage,
    quota_info: dict = Depends(check_message_quota),
    db: Session = Depends(get_db)
):
    """与AI对话处理Excel（集成用户验证和消息限制）"""
    try:
        # 记录消息到数据库
        create_message_record(
            db=db,
            user_id=quota_info["user_id"],
            trial_cookie=quota_info["trial_cookie"],
            message_content=chat_data.message
        )
        
        # 从全局变量获取对话会话（这里需要访问main.py中的conversation_sessions）
        # 为了简化，我们将这个逻辑保留在main.py中，这里只是一个示例结构
        
        return {
            "success": True,
            "message": "消息已记录，配额检查通过",
            "quota_info": quota_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        error_details = f"处理失败: {str(e)}\n错误堆栈:\n{traceback.format_exc()}"
        print(error_details)
        raise HTTPException(status_code=500, detail=str(e))
