/**
 * Excel对话编辑系统主逻辑文件
 * 包含核心业务逻辑：文件上传、配置管理、聊天功能等
 */

// 全局变量
let currentFileId = null;
let currentDownloadId = null;
let currentConversationId = null;  // 当前对话ID

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initUserStatus();
});

// 初始化用户状态
async function initUserStatus() {
    try {
        if (authManager.isLoggedIn()) {
            // 已登录用户：获取用户信息和配额
            const user = await authManager.getUserInfo();
            if (user) {
                showLoggedInStatus(user);
            } else {
                // Token可能已过期，显示未登录状态
                showNotLoggedInStatus();
            }
        } else {
            // 未登录用户：初始化试用状态
            await initTrialStatus();
        }
    } catch (error) {
        console.error('初始化用户状态失败:', error);
        showNotLoggedInStatus();
    }
}

// 显示登录用户状态
function showLoggedInStatus(user) {
    document.getElementById('loggedInStatus').style.display = 'flex';
    document.getElementById('trialStatus').style.display = 'none';
    document.getElementById('notLoggedInStatus').style.display = 'none';

    // 设置用户头像（显示昵称首字符）
    const avatar = document.getElementById('userAvatar');
    avatar.textContent = user.nickname.charAt(0).toUpperCase();

    // 设置用户信息
    document.getElementById('userNickname').textContent = user.nickname;
    document.getElementById('userQuota').textContent = '每日30条消息';
}

// 显示试用状态
function showTrialStatus(trialData) {
    document.getElementById('loggedInStatus').style.display = 'none';
    document.getElementById('trialStatus').style.display = 'block';
    document.getElementById('notLoggedInStatus').style.display = 'none';

    const message = `已使用 ${trialData.usage_count}/${trialData.usage_count + trialData.remaining_count} 次`;
    document.getElementById('trialMessage').textContent = message;
}

// 显示未登录状态
function showNotLoggedInStatus() {
    document.getElementById('loggedInStatus').style.display = 'none';
    document.getElementById('trialStatus').style.display = 'none';
    document.getElementById('notLoggedInStatus').style.display = 'block';
}

// 初始化试用状态
async function initTrialStatus() {
    try {
        // 先尝试检查现有试用状态
        let trialData = await authManager.checkTrialStatus();

        if (!trialData) {
            // 如果没有试用记录，初始化一个
            trialData = await authManager.initTrial();
        }

        if (trialData && trialData.success) {
            if (trialData.limit_reached) {
                // 试用次数已用完，显示注册提示
                showTrialLimitReached();
            } else {
                // 显示试用状态
                showTrialStatus(trialData);
                // 更新未登录状态中的试用次数显示
                document.getElementById('trialCount').textContent = trialData.remaining_count;
            }
        } else {
            showNotLoggedInStatus();
        }
    } catch (error) {
        console.error('初始化试用状态失败:', error);
        showNotLoggedInStatus();
    }
}

// 显示试用次数用完提示
function showTrialLimitReached() {
    document.getElementById('loggedInStatus').style.display = 'none';
    document.getElementById('trialStatus').style.display = 'block';
    document.getElementById('notLoggedInStatus').style.display = 'none';

    document.getElementById('trialMessage').textContent = '试用次数已用完，请注册账号继续使用';
}

// 处理登出
async function handleLogout() {
    if (confirm('确定要登出吗？')) {
        await authManager.logout();
    }
}

// 显示试用限制弹窗
function showTrialLimitModal() {
    const modal = document.createElement('div');
    modal.className = 'trial-limit-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>试用次数已用完</h3>
            </div>
            <div class="modal-body">
                <p>您的试用次数已用完（3次）。</p>
                <p>注册账号即可享受每日30条消息的完整服务！</p>
            </div>
            <div class="modal-footer">
                <button onclick="closeTrialLimitModal()" class="btn-secondary">稍后再说</button>
                <a href="/register" class="btn-primary">立即注册</a>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加样式
    if (!document.getElementById('modal-styles')) {
        const style = document.createElement('style');
        style.id = 'modal-styles';
        style.textContent = `
            .trial-limit-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }
            .modal-content {
                background: white;
                border-radius: 12px;
                padding: 0;
                max-width: 400px;
                width: 90%;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            }
            .modal-header {
                padding: 20px 20px 10px;
                border-bottom: 1px solid #eee;
            }
            .modal-header h3 {
                margin: 0;
                color: #333;
            }
            .modal-body {
                padding: 20px;
                color: #666;
                line-height: 1.6;
            }
            .modal-footer {
                padding: 10px 20px 20px;
                display: flex;
                gap: 10px;
                justify-content: flex-end;
            }
            .btn-secondary {
                background: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
            }
            .btn-primary {
                background: #667eea;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
            }
        `;
        document.head.appendChild(style);
    }
}

// 关闭试用限制弹窗
function closeTrialLimitModal() {
    const modal = document.querySelector('.trial-limit-modal');
    if (modal) {
        modal.remove();
    }
}

// 从.env加载配置
async function loadEnvConfig() {
    try {
        showMessage('configMessage', '正在从.env文件加载配置...', 'info');
        const response = await fetch('/config/status', {
            method: 'GET'
        });
        
        const result = await response.json();
        if (result.success && result.has_env_config) {
            // 填充界面配置
            document.getElementById('apiUrl').value = result.env_config.api_url;
            document.getElementById('modelName').value = result.env_config.model_name || 'gpt-3.5-turbo';
            
            showMessage('configMessage', '已从.env文件加载配置！请输入API Key并保存。', 'success');
            updateConfigStatus(result);
        } else if (result.success && !result.has_env_config) {
            showMessage('configMessage', '未找到.env文件或文件中缺少必要配置', 'error');
            updateConfigStatus(result);
        } else {
            showMessage('configMessage', '加载配置失败: ' + result.error, 'error');
        }
    } catch (error) {
        showMessage('configMessage', '加载配置失败: ' + error.message, 'error');
    }
}

// 更新配置状态显示
function updateConfigStatus(result) {
    const statusElement = document.getElementById('configStatus');
    let statusText = '';
    
    if (result.has_env_config) {
        statusText += '✅ .env配置已找到 ';
    } else {
        statusText += '❌ 未找到.env配置 ';
    }
    
    if (result.has_manual_config) {
        statusText += '✅ 界面配置已设置';
    } else {
        statusText += '❌ 界面配置未设置';
    }
    
    statusElement.innerHTML = statusText;
}

// 保存OpenAI配置
async function saveConfig() {
    const apiUrl = document.getElementById('apiUrl').value;
    const apiKey = document.getElementById('apiKey').value;
    const modelName = document.getElementById('modelName').value;
    
    if (!apiUrl || !apiKey || !modelName) {
        showMessage('configMessage', '请填写完整的配置信息', 'error');
        return;
    }
    
    try {
        const response = await fetch('/config/openai', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                api_url: apiUrl,
                api_key: apiKey,
                model_name: modelName
            })
        });
        
        const result = await response.json();
        if (result.success) {
            showMessage('configMessage', '配置保存成功！', 'success');
        } else {
            showMessage('configMessage', result.message, 'error');
        }
    } catch (error) {
        showMessage('configMessage', '保存配置失败: ' + error.message, 'error');
    }
}

// 测试连接
async function testConnection() {
    try {
        showMessage('configMessage', '正在测试连接...', 'info');
        const response = await fetch('/config/test', {
            method: 'POST'
        });
        
        const result = await response.json();
        if (result.success) {
            showMessage('configMessage', result.message, 'success');
        } else {
            showMessage('configMessage', result.message, 'error');
        }
    } catch (error) {
        showMessage('configMessage', '测试连接失败: ' + error.message, 'error');
    }
}

// 选择并上传文件
function selectAndUploadFile() {
    const fileInput = document.getElementById('excelFile');
    fileInput.click();
}

// 上传文件
async function uploadFile() {
    const fileInput = document.getElementById('excelFile');
    const file = fileInput.files[0];
    
    if (!file) {
        showMessage('uploadMessage', '请选择一个Excel文件', 'error');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        showMessage('uploadMessage', '正在上传文件...', 'info');
        const response = await fetch('/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        if (result.success) {
            currentFileId = result.file_id;
            showMessage('uploadMessage', '文件上传成功！', 'success');
            displayFileInfo(result);
        } else {
            showMessage('uploadMessage', result.detail || '上传失败', 'error');
        }
    } catch (error) {
        showMessage('uploadMessage', '上传失败: ' + error.message, 'error');
    }
}

// 显示文件信息
function displayFileInfo(result) {
    const fileInfo = document.getElementById('fileInfo');
    const fileInfoHeader = fileInfo.querySelector('.file-info-header');
    const fileInfoContent = document.getElementById('fileInfoContent');
    const toggle = fileInfo.querySelector('.file-info-toggle');
    const content = fileInfo.querySelector('.file-info-content');
    
    // 更新标题
    fileInfoHeader.innerHTML = `
        <span>📄 文件信息: ${result.filename}</span>
        <span class="file-info-toggle">▼</span>
    `;
    
    // 更新文件详细信息
    let contentHtml = '';
    
    for (const [sheetName, info] of Object.entries(result.sheet_info)) {
        contentHtml += `<div class="sheet-info">`;
        contentHtml += `<strong>Sheet "${sheetName}":</strong><br>`;
        contentHtml += `- 行数: ${info.rows}<br>`;
        contentHtml += `- 列数: ${info.columns}<br>`;
        contentHtml += `- 列名: ${info.column_names.join(', ')}<br>`;
        contentHtml += `</div>`;
    }
    
    fileInfoContent.innerHTML = contentHtml;
    
    // 确保默认是折叠状态
    content.classList.remove('show');
    toggle.classList.remove('expanded');
    
    // 显示文件信息区域
    fileInfo.classList.remove('hidden');
    
    // 确保文件信息默认是展开的
    fileInfoContent.classList.add('show');
    const toggleElement = document.querySelector('.file-info-toggle');
    if (toggleElement) {
        toggleElement.classList.remove('collapsed');
    }
    
    // 上传成功后显示对话功能
    showConversationSection();
}

// 检查AI返回结果是否包含文件修改
function checkIfFileWasModified(result) {
    // 检查result对象中的file_modified字段
    if (result.hasOwnProperty('file_modified')) {
        return result.file_modified;
    }

    // 如果没有明确的file_modified字段，通过其他信息判断
    // 如果有download_url，通常意味着文件被修改了
    if (result.download_url) {
        return true;
    }

    // 如果类型是代码执行且有执行输出，可能修改了文件
    if (result.type === 'code_execution' && result.execution_output) {
        // 检查执行输出中是否包含文件保存相关的关键词
        const output = result.execution_output.toLowerCase();
        if (output.includes('保存') || output.includes('写入') || output.includes('save') ||
            output.includes('已完成') || output.includes('成功')) {
            return true;
        }
    }

    // 默认情况下假设没有修改文件
    return false;
}

// 发送消息函数
async function sendMessage() {
    const message = document.getElementById('userMessage').value;
    const sendBtn = document.getElementById('sendBtn');

    if (!message.trim()) {
        showMessage('chatMessage', '请输入您的需求', 'error');
        return;
    }

    if (!currentFileId) {
        showMessage('chatMessage', '请先上传Excel文件', 'error');
        return;
    }

    // 检查用户状态和配额
    if (!authManager.isLoggedIn()) {
        // 未登录用户：检查试用状态
        const trialData = await authManager.checkTrialStatus();
        if (!trialData || trialData.limit_reached) {
            showTrialLimitModal();
            return;
        }
    }

    // 添加用户消息到聊天框
    addMessageToChat('user', message);
    document.getElementById('userMessage').value = '';

    // 禁用发送按钮
    sendBtn.disabled = true;
    sendBtn.innerHTML = '<span class="loading-spinner"></span>';

    // 记录开始时间
    const startTime = Date.now();

    // 创建AI消息占位符，包含思考过程
    const aiMessageId = createAIMessageWithThinking();

    try {
        // 步骤1: 准备请求
        addThinkingStep(aiMessageId, 'step1', 'pending', '📝 准备提交请求给AI模型');
        updateThinkingStep(aiMessageId, 'step1', 'processing', '📝 正在提交请求给AI模型');

        const requestBody = {
            message: message,
            file_id: currentFileId
        };

        // 如果有当前对话ID，添加到请求中
        if (currentConversationId) {
            requestBody.conversation_id = currentConversationId;
        }

        updateThinkingStep(aiMessageId, 'step1', 'success', '✅ 请求已提交给AI模型');

        // 步骤2: 等待AI响应
        addThinkingStep(aiMessageId, 'step2', 'processing', '🤖 AI模型正在分析并生成回复');

        const response = await fetch('/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...authManager.getAuthHeaders()
            },
            credentials: 'include',  // 包含cookies（用于试用用户）
            body: JSON.stringify(requestBody)
        });

        const result = await response.json();

        updateThinkingStep(aiMessageId, 'step2', 'success', '✅ AI模型已完成分析');

        if (result.success) {
            // 步骤3: 处理AI响应
            addThinkingStep(aiMessageId, 'step3', 'processing', '📋 正在处理AI响应');

            // 根据返回类型构建AI回复消息
            let aiMessage = result.message;
            let hasFileModification = false;

            if (result.type === 'code_execution') {
                // 检查是否有文件修改
                hasFileModification = checkIfFileWasModified(result);

                updateThinkingStep(aiMessageId, 'step3', 'success', '✅ AI响应处理完成 - 代码执行类型');

                // 步骤4: 显示生成的代码
                if (result.generated_code) {
                    addThinkingStep(aiMessageId, 'step4', 'success', '📄 生成的Python代码', result.generated_code);
                }

                // 步骤5: 显示执行结果
                if (result.execution_output) {
                    addThinkingStep(aiMessageId, 'step5', 'success', '💻 代码执行输出', result.execution_output);
                }

                // 步骤6: 文件处理结果
                if (hasFileModification) {
                    if (result.download_url) {
                        addThinkingStep(aiMessageId, 'step6', 'success', '📁 文件已修改，可以下载');
                        aiMessage += '<br><br>📁 <a href="' + result.download_url + '" class="download-link" target="_blank">下载处理后的文件</a>';
                    } else {
                        addThinkingStep(aiMessageId, 'step6', 'success', '📁 文件已修改');
                    }
                } else {
                    addThinkingStep(aiMessageId, 'step6', 'success', '📊 仅查询操作，无文件修改');
                }
            } else {
                updateThinkingStep(aiMessageId, 'step3', 'success', '✅ AI响应处理完成 - 直接回答类型');
                addThinkingStep(aiMessageId, 'step4', 'success', '💬 AI直接回答，无需代码执行');
            }

            // 计算耗时
            const duration = ((Date.now() - startTime) / 1000).toFixed(1);

            // 完成思考过程并自动折叠
            completeThinkingProcess(aiMessageId, duration);

            // 更新AI消息内容
            updateAIMessageContent(aiMessageId, aiMessage);

            // 更新对话ID（如果返回了新的对话ID）
            if (result.conversation_id) {
                currentConversationId = result.conversation_id;
            }

            currentDownloadId = result.download_id;
            showMessage('chatMessage', '处理完成！', 'success');
        } else {
            updateThinkingStep(aiMessageId, 'step2', 'error', '❌ AI处理失败: ' + (result.detail || result.message));

            // 计算耗时
            const duration = ((Date.now() - startTime) / 1000).toFixed(1);
            completeThinkingProcess(aiMessageId, duration);

            updateAIMessageContent(aiMessageId, '处理失败: ' + (result.detail || result.message));
            showMessage('chatMessage', result.detail || result.message, 'error');
        }
    } catch (error) {
        // 如果有步骤2，更新为错误状态
        const step2Element = document.querySelector(`#${aiMessageId} .thinking-step[data-step="step2"]`);
        if (step2Element) {
            updateThinkingStep(aiMessageId, 'step2', 'error', '❌ 网络错误或服务器异常: ' + error.message);
        } else {
            addThinkingStep(aiMessageId, 'step2', 'error', '❌ 网络错误或服务器异常: ' + error.message);
        }

        // 计算耗时
        const duration = ((Date.now() - startTime) / 1000).toFixed(1);
        completeThinkingProcess(aiMessageId, duration);

        updateAIMessageContent(aiMessageId, '处理失败: ' + error.message);
        showMessage('chatMessage', '处理失败: ' + error.message, 'error');
    } finally {
        // 恢复发送按钮
        sendBtn.disabled = false;
        sendBtn.innerHTML = '<span class="send-arrow"></span>';
    }
}

// 页面加载时自动检查配置状态
document.addEventListener('DOMContentLoaded', function() {
    // 初始化tab状态 - 确保只有主页面tab是激活的
    const allButtons = document.querySelectorAll('.tab-button');
    allButtons.forEach(btn => btn.classList.remove('active'));

    const mainTabButton = document.querySelector(`button[onclick="switchTab('main')"]`);
    if (mainTabButton) {
        mainTabButton.classList.add('active');
    }

    // 确保只有主页面tab内容显示
    const allTabs = document.querySelectorAll('.tab-content');
    allTabs.forEach(tab => tab.classList.remove('active'));

    const mainTabContent = document.getElementById('main-tab');
    if (mainTabContent) {
        mainTabContent.classList.add('active');
    }

    // 检查配置状态
    fetch('/config/status')
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                updateConfigStatus(result);
            }
        })
        .catch(error => {
            console.log('检查配置状态失败:', error);
        });

    // 检查marked.js是否正确加载
    setTimeout(() => {
        console.log('marked.js检查:');
        console.log('typeof marked:', typeof marked);
        if (typeof marked !== 'undefined') {
            console.log('marked.parse:', typeof marked.parse);
            console.log('marked as function:', typeof marked === 'function');

            // 测试markdown解析
            try {
                let testText = '**粗体测试** 和 *斜体测试*';
                let result = '';
                if (typeof marked.parse === 'function') {
                    result = marked.parse(testText);
                } else if (typeof marked === 'function') {
                    result = marked(testText);
                }
                console.log('测试markdown解析:', testText, '=>', result);
            } catch (e) {
                console.error('markdown测试失败:', e);
            }
        } else {
            console.error('marked.js未加载');
        }
    }, 1000);

    const fileInput = document.getElementById('excelFile');
    fileInput.addEventListener('change', function() {
        if (this.files[0]) {
            uploadFile();
        }
    });

    // 聊天输入框增强功能
    const userMessageTextarea = document.getElementById('userMessage');

    // 自动调整textarea高度
    function autoResizeTextarea() {
        userMessageTextarea.style.height = 'auto';
        userMessageTextarea.style.height = Math.min(userMessageTextarea.scrollHeight, 120) + 'px';
    }

    // 监听输入事件，自动调整高度
    userMessageTextarea.addEventListener('input', autoResizeTextarea);

    // 监听键盘事件，支持Enter发送
    userMessageTextarea.addEventListener('keydown', function(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            sendMessage();
        } else if (event.key === 'Enter' && event.shiftKey) {
            // Shift+Enter 换行，保持默认行为
            setTimeout(autoResizeTextarea, 0);
        }
    });

    // 初始化高度
    autoResizeTextarea();
});
